package com.tunnel.vnc.util;

import com.alibaba.fastjson.JSONObject;

/**
 * 统一结果返回工具类
 * 基于反编译的vnc-api实现
 * 
 * <AUTHOR>
 */
public class ResultUtil {

    /**
     * 成功结果（无数据）
     */
    public static JSONObject successResult() {
        JSONObject result = new JSONObject();
        result.put("code", ResultCodeEnum.SUCCESS.getCode());
        result.put("result", "success");
        return result;
    }

    /**
     * 成功结果（带数据）
     */
    public static JSONObject successResult(Object data) {
        JSONObject result = new JSONObject();
        result.put("code", ResultCodeEnum.SUCCESS.getCode());
        result.put("result", data);
        return result;
    }

    /**
     * 错误结果
     */
    public static JSONObject errorResult(String message) {
        JSONObject result = new JSONObject();
        result.put("code", ResultCodeEnum.ERROR.getCode());
        result.put("message", message);
        return result;
    }
}
