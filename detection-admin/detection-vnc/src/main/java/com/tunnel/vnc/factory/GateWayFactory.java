package com.tunnel.vnc.factory;

import com.alibaba.fastjson.JSONObject;
import com.tunnel.vnc.config.VncConfig;
import com.tunnel.vnc.util.HttpUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * 网关工厂类 - 负责与GM服务通信
 * 基于反编译的vnc-api实现
 * 
 * <AUTHOR>
 */
@Component
public class GateWayFactory {

    private static final Logger log = LoggerFactory.getLogger(GateWayFactory.class);

    @Autowired
    private VncConfig vncConfig;

    /**
     * 获取GTA结果 - 核心方法
     */
    public JSONObject getGTAResult(String id) throws Exception {
        // 1. 登录获取session
        String sessionCode = login();
        
        try {
            // 2. 构建查询URL
            String queryUrl = String.format("http://%s:%s/app/cgi/%s/ws.cgi", 
                vncConfig.getHost(), vncConfig.getPort(), sessionCode);
            
            // 3. 构建查询参数
            Map<String, String> params = new HashMap<>();
            params.put("op", "query");
            params.put("id", id);
            
            // 4. 发送查询请求
            String response = HttpUtil.sendPost(queryUrl, params);
            JSONObject result = JSONObject.parseObject(response);
            
            // 5. 检查响应状态
            if (result == null || !"ok".equals(result.getString("status"))) {
                throw new Exception("查询VNC设备信息失败");
            }
            
            return result;
            
        } finally {
            // 6. 登出
            try {
                logout(sessionCode);
            } catch (Exception e) {
                log.warn("登出GM服务异常", e);
            }
        }
    }

    /**
     * 登录GM服务
     */
    private String login() throws Exception {
        // 1. 初始化连接
        Map<String, String> initParams = new HashMap<>();
        initParams.put("op", "init");
        
        String initUrl = String.format("http://%s:%s/app/cgi/ws.cgi", 
            vncConfig.getHost(), vncConfig.getPort());
        String initResponse = HttpUtil.sendPost(initUrl, initParams);
        JSONObject initResult = JSONObject.parseObject(initResponse);
        
        if (initResult == null || initResult.getString("code") == null || "".equals(initResult.getString("code"))) {
            throw new Exception("GM服务已断开，请联系系统管理员！");
        }
        
        String code = initResult.getString("code");
        
        // 2. 登录
        String loginUrl = String.format("http://%s:%s/app/cgi/%s/ws.cgi", 
            vncConfig.getHost(), vncConfig.getPort(), code);
        Map<String, String> loginParams = new HashMap<>();
        loginParams.put("op", "login");
        loginParams.put("user", vncConfig.getUsername());
        loginParams.put("pass", vncConfig.getPassword());
        
        String loginResponse = HttpUtil.sendPost(loginUrl, loginParams);
        JSONObject loginResult = JSONObject.parseObject(loginResponse);
        
        if (loginResult == null || !"ok".equals(loginResult.getString("status"))) {
            throw new Exception("GM服务已断开，请联系系统管理员！");
        }
        
        return code;
    }

    /**
     * 登出GM服务
     */
    private void logout(String sessionCode) throws IOException {
        String logoutUrl = String.format("http://%s:%s/app/cgi/%s/ws.cgi", 
            vncConfig.getHost(), vncConfig.getPort(), sessionCode);
        Map<String, String> params = new HashMap<>();
        params.put("op", "logout");
        
        HttpUtil.sendPost(logoutUrl, params);
    }
}
