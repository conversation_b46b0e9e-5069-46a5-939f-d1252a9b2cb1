package com.tunnel.vnc.util;

/**
 * 结果状态码枚举
 * 基于反编译的vnc-api实现
 * 
 * <AUTHOR>
 */
public enum ResultCodeEnum {
    
    SUCCESS("200", "成功"),
    ERROR("500", "错误");

    private final String code;
    private final String message;

    ResultCodeEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public String getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
}
