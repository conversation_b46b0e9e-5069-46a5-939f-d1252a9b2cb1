package com.tunnel.vnc.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * VNC配置类
 * 基于反编译的vnc-api实现
 * 
 * <AUTHOR>
 */
@Component
@ConfigurationProperties(prefix = "vnc.gm")
public class VncConfig {

    private String host;
    private String port;
    private String username;
    private String password;
    private int timeout = 30000;

    public String getHost() {
        return host;
    }

    public void setHost(String host) {
        this.host = host;
    }

    public String getPort() {
        return port;
    }

    public void setPort(String port) {
        this.port = port;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public int getTimeout() {
        return timeout;
    }

    public void setTimeout(int timeout) {
        this.timeout = timeout;
    }
}
