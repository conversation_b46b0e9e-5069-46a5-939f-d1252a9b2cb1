package com.tunnel.vnc.controller;

import com.alibaba.fastjson.JSONObject;
import com.tunnel.vnc.factory.GateWayFactory;
import com.tunnel.vnc.util.ResultUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * VNC API控制器
 * 基于反编译的vnc-api实现
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api")
public class ApiController {

    private static final Logger log = LoggerFactory.getLogger(ApiController.class);

    @Autowired
    private GateWayFactory gateWayFactory;

    /**
     * 根据ID获取VNC连接URL
     * 复制自反编译的ApiCtrl.getVNCUrlById方法
     */
    @GetMapping("/getVNCUrlById")
    public JSONObject getVNCUrlById(HttpServletRequest request, 
                                   HttpServletResponse response, 
                                   @RequestParam("id") String id) {
        try {
            // 1. 调用GateWayFactory获取连接信息
            JSONObject result = gateWayFactory.getGTAResult(id);
            
            // 2. 构建VNC URL - 基于反编译的字节码逻辑
            StringBuilder urlBuilder = new StringBuilder();
            urlBuilder.append("vnc.html?encrypt=1&autoconnect=1&autoclose=1&host=")
                     .append(result.getString("host"))
                     .append("&port=")
                     .append(result.getString("port"))
                     .append("&path=wss/vnc/")
                     .append(id)
                     .append("/")
                     .append(result.getString("session"));
            
            String vncUrl = urlBuilder.toString();
            
            // 3. 构建响应对象
            JSONObject responseData = new JSONObject();
            responseData.put("url", vncUrl);
            
            // 4. 返回成功结果
            return ResultUtil.successResult(responseData);
            
        } catch (Exception e) {
            // 5. 异常处理 - 记录日志并返回错误结果
            log.warn("getVNCUrlById warn:" + e.getMessage(), e);
            return ResultUtil.errorResult(e.getMessage());
        }
    }

    /**
     * 列出VNC设备
     * 基于反编译的ApiCtrl.listVNCDevice方法
     */
    @GetMapping("/listVNCDevice")
    public JSONObject listVNCDevice(HttpServletRequest request, HttpServletResponse response) {
        try {
            // 这里可以实现设备列表逻辑，暂时返回空列表
            JSONObject responseData = new JSONObject();
            responseData.put("devices", new Object[0]);
            
            return ResultUtil.successResult(responseData);
            
        } catch (Exception e) {
            log.warn("listVNCDevice warn:" + e.getMessage(), e);
            return ResultUtil.errorResult(e.getMessage());
        }
    }

    /**
     * 根据MAC地址列出设备点位
     * 基于反编译的ApiCtrl.listDevPointByMac方法
     */
    @GetMapping("/listDevPointByMac")
    public JSONObject listDevPointByMac(HttpServletRequest request, 
                                       HttpServletResponse response,
                                       @RequestParam("mac") String mac) {
        try {
            // 这里可以实现根据MAC地址查询设备点位的逻辑
            JSONObject responseData = new JSONObject();
            responseData.put("points", new Object[0]);
            
            return ResultUtil.successResult(responseData);
            
        } catch (Exception e) {
            log.warn("listDevPointByMac warn:" + e.getMessage(), e);
            return ResultUtil.errorResult(e.getMessage());
        }
    }
}
