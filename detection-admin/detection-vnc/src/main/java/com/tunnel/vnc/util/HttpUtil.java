package com.tunnel.vnc.util;

import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.Map;

/**
 * HTTP工具类
 * 基于反编译的vnc-api实现
 * 
 * <AUTHOR>
 */
public class HttpUtil {

    private static final Logger log = LoggerFactory.getLogger(HttpUtil.class);

    /**
     * 发送POST请求
     */
    public static String sendPost(String url, Map<String, String> params) throws IOException {
        long startTime = System.currentTimeMillis();
        
        CloseableHttpClient httpClient = HttpClients.createDefault();
        CloseableHttpResponse response = null;
        String result = null;
        
        try {
            HttpPost httpPost = new HttpPost(url);
            
            // 设置请求头
            httpPost.setHeader("Content-Type", "application/x-www-form-urlencoded");
            
            // 构建请求参数
            StringBuilder paramStr = new StringBuilder();
            for (Map.Entry<String, String> entry : params.entrySet()) {
                if (paramStr.length() > 0) {
                    paramStr.append("&");
                }
                paramStr.append(entry.getKey()).append("=").append(entry.getValue());
            }
            
            // 设置请求体
            StringEntity entity = new StringEntity(paramStr.toString(), "UTF-8");
            httpPost.setEntity(entity);
            
            // 执行请求
            response = httpClient.execute(httpPost);
            HttpEntity responseEntity = response.getEntity();
            
            if (responseEntity != null) {
                result = EntityUtils.toString(responseEntity, "UTF-8");
            }
            
        } catch (Exception e) {
            log.error("HTTP请求异常", e);
            throw e;
        } finally {
            // 释放资源
            try {
                if (response != null) {
                    response.close();
                }
                if (httpClient != null) {
                    httpClient.close();
                }
            } catch (IOException e) {
                log.error("HTTP请求释放资源异常", e);
            }
            
            long endTime = System.currentTimeMillis();
            log.info("请求{}接口,本次请求API接口的响应时间为:{}毫秒", url, endTime - startTime);
        }
        
        return result;
    }
}
