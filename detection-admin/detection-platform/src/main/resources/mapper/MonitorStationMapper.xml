<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tunnel.mapper.MonitorStationMapper">
    
    <resultMap type="com.tunnel.domain.MonitorStation" id="MonitorStationResult">
        <result property="id"    column="id"    />
        <result property="type"    column="type"    />
        <result property="code"    column="code"    />
        <result property="city"    column="city"    />
        <result property="district"    column="district"    />
        <result property="lat"    column="lat"    />
        <result property="lon"    column="lon"    />
        <result property="name"    column="name"    />
        <result property="province"    column="province"    />
        <result property="state"    column="state"    />
        <result property="pollutionType"    column="pollution_type"    />
        <result property="faulty"    column="faulty"    />
        <result property="stationindex"    column="stationIndex"    />
        <result property="remark"    column="remark"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="creator"    column="creator"    />
        <result property="modifier"    column="modifier"    />
    </resultMap>

    <sql id="allColumn">
        id, type, code, city, district, lat, lon, name, province, state, pollution_type, faulty, stationIndex, remark, create_time, update_time, creator, modifier
    </sql>

    <select id="selectMonitorStationList" parameterType="com.tunnel.domain.MonitorStation" resultType="com.tunnel.domain.MonitorStation">
        SELECT m.*,s.system_code from sc_monitor_station m
        INNER JOIN sc_integration_system s on m.code=s.monitor_station_code
        <where>
            <if test="type != null  and type != ''"> and s.type = #{type}</if>
            <if test="name != null  and name != ''"> and m.name like concat('%', #{name}, '%')</if>
            <if test="code != null  and code != ''"> and m.code = #{code}</if>
            <if test="city != null  and city != ''"> and m.city = #{city}</if>
            <if test="district != null  and district != ''"> and m.district = #{district}</if>
            <if test="province != null  and province != ''"> and m.province = #{province}</if>
            <if test="state != null "> and m.state = #{state}</if>
            <if test="pollutionType != null  and pollutionType != ''"> and m.pollution_type = #{pollutionType}</if>
        </where>
    </select>
    
    <select id="selectMonitorStationById" parameterType="Long" resultMap="MonitorStationResult">
        select <include refid="allColumn"/> from <include refid="tableName"/>
        where id = #{id}
    </select>
    <select id="selectMonitorStationByType" resultType="com.tunnel.domain.MonitorStation">
        SELECT m.*,s.system_code from sc_monitor_station m
        INNER JOIN sc_integration_system s on m.code=s.monitor_station_code
        WHERE s.type=#{type}
    </select>

    <insert id="insertMonitorStation" parameterType="com.tunnel.domain.MonitorStation" useGeneratedKeys="true" keyProperty="id">
        insert into <include refid="tableName"/>
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="type != null and type != ''">type,</if>
            <if test="code != null and code != ''">code,</if>
            <if test="city != null">city,</if>
            <if test="district != null">district,</if>
            <if test="lat != null">lat,</if>
            <if test="lon != null">lon,</if>
            <if test="name != null">name,</if>
            <if test="province != null">province,</if>
            <if test="state != null">state,</if>
            <if test="pollutionType != null">pollution_type,</if>
            <if test="faulty != null">faulty,</if>
            <if test="stationindex != null">stationIndex,</if>
            <if test="remark != null">remark,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="creator != null">creator,</if>
            <if test="modifier != null">modifier,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="type != null and type != ''">#{type},</if>
            <if test="code != null and code != ''">#{code},</if>
            <if test="city != null">#{city},</if>
            <if test="district != null">#{district},</if>
            <if test="lat != null">#{lat},</if>
            <if test="lon != null">#{lon},</if>
            <if test="name != null">#{name},</if>
            <if test="province != null">#{province},</if>
            <if test="state != null">#{state},</if>
            <if test="pollutionType != null">#{pollutionType},</if>
            <if test="faulty != null">#{faulty},</if>
            <if test="stationindex != null">#{stationindex},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="creator != null">#{creator},</if>
            <if test="modifier != null">#{modifier},</if>
         </trim>
    </insert>

    <update id="updateMonitorStation" parameterType="com.tunnel.domain.MonitorStation">
        update <include refid="tableName"/>
        <trim prefix="SET" suffixOverrides=",">
            <if test="type != null and type != ''">type = #{type},</if>
            <if test="code != null and code != ''">code = #{code},</if>
            <if test="city != null">city = #{city},</if>
            <if test="district != null">district = #{district},</if>
            <if test="lat != null">lat = #{lat},</if>
            <if test="lon != null">lon = #{lon},</if>
            <if test="name != null">name = #{name},</if>
            <if test="province != null">province = #{province},</if>
            <if test="state != null">state = #{state},</if>
            <if test="pollutionType != null">pollution_type = #{pollutionType},</if>
            <if test="faulty != null">faulty = #{faulty},</if>
            <if test="stationindex != null">stationIndex = #{stationindex},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="creator != null">creator = #{creator},</if>
            <if test="modifier != null">modifier = #{modifier},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMonitorStationById" parameterType="Long">
        delete from <include refid="tableName"/> where id = #{id}
    </delete>

    <delete id="deleteMonitorStationByIds" parameterType="String">
        delete from <include refid="tableName"/> where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <sql id="tableName">
        sc_monitor_station
    </sql>
</mapper>