<template>
  <div class="left-card">
    <m-card title="各区经济收益">
      <v-chart ref="vChart" :option="option" :autoresize="true" />
    </m-card>
  </div>
</template>
<script setup>
import { ref, onMounted,onBeforeUnmount, nextTick } from "vue"
import * as echarts from "echarts"
import mCard from "@/components/mCard/index.vue"
import VChart from "vue-echarts"
import "echarts-gl"

const option = ref({
  title: {
    text: "亿元",
    left: "5%",
    top: "8%",
    textStyle: {
      color: "#D3F8F2",
      fontSize: 8,
    },
  },
  grid3D: {
    boxWidth: 200,
    boxDepth: 80,
    boxHeight: 120,
    alpha: 15,
    beta: 30,
    light: {
      main: {
        intensity: 1.2,
        shadow: true,
        shadowQuality: 'high',
        alpha: 30,
        beta: 40
      },
      ambient: {
        intensity: 0.3
      }
    },
    environment: 'none',
    postEffect: {
      enable: true,
      bloom: {
        enable: true,
        bloomIntensity: 0.1
      }
    },
    viewControl: {
      alpha: 15,
      beta: 30,
      distance: 200,
      autoRotate: false
    }
  },
  tooltip: {
    backgroundColor: "rgba(0,0,0,1)",
    borderWidth: 1,
    borderColor: "#999999",
    textStyle: {
      color: "#ffffff",
      fontSize: 10,
    },
  },
  xAxis3D: {
    type: "category",
    data: ["南山区", "天河区", "福田区", "龙岗区", "宝安区"],
    axisLabel: {
      color: "#ffffff",
      fontSize: 8,
    },
    axisLine: {
      lineStyle: {
        color: "#435459"
      }
    }
  },
  yAxis3D: {
    type: "value",
    axisLabel: {
      color: "#8B9EA4",
      fontSize: 8,
    },
    axisLine: {
      lineStyle: {
        color: "#435459"
      }
    },
    splitLine: {
      lineStyle: {
        color: "rgba(255, 255, 255, 0.3)"
      }
    }
  },
  zAxis3D: {
    type: "value",
    axisLabel: {
      color: "#8B9EA4",
      fontSize: 8,
    }
  },
  series: [
    {
      type: "bar3D",
      data: [
        {
          value: [0, 8000, 0],
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: "rgba(64, 175, 255, 1)" },
              { offset: 1, color: "rgba(64, 175, 255, 0.3)" },
            ]),
          },
        },
        {
          value: [1, 7500, 0],
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: "rgba(25, 255, 198, 1)" },
              { offset: 1, color: "rgba(0, 204, 187, 0.3)" },
            ]),
          },
        },
        {
          value: [2, 6000, 0],
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: "rgba(64, 175, 255, 1)" },
              { offset: 1, color: "rgba(64, 175, 255, 0.3)" },
            ]),
          },
        },
        {
          value: [3, 5800, 0],
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: "rgba(25, 255, 198, 1)" },
              { offset: 1, color: "rgba(0, 204, 187, 0.3)" },
            ]),
          },
        },
        {
          value: [4, 5000, 0],
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: "rgba(64, 175, 255, 1)" },
              { offset: 1, color: "rgba(64, 175, 255, 0.3)" },
            ]),
          },
        },
      ],
      shading: 'realistic',
      realisticMaterial: {
        roughness: 0.4,
        metalness: 0.2
      },
      label: {
        show: true,
        distance: 10,
        textStyle: {
          color: "#ffffff",
          fontSize: 10,
        },
        formatter: function(params) {
          return params.value[1];
        }
      }
    },
    {
      name: "",
      type: "custom",
      z: 3,
      xAxisIndex: 1,
      renderItem: (params, api) => {
        const categoryIndex = api.value(0)

        const categoryData = api.value(1)

        const basicsCoord = api.coord([categoryIndex, categoryData])

        const topBasicsYAxis = basicsCoord[1]

        const basicsXAxis = basicsCoord[0]
        return {
          type: "image",
          style: {
            x: basicsXAxis - 5,
            y: topBasicsYAxis - 5,
            width: 10,
            height: 10,
            image:
              "data:image/png;base64,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",
          },
        }
      },
      data: [8000, 7500, 6000, 5800, 5000],
    },
  ],
})


</script>
<style lang="scss"></style>
