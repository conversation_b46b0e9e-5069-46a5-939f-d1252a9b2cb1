<template>
  <div class="left-card">
    <m-card title="近年经济情况">
      <v-chart ref="vChart" :option="option" :autoresize="true" />
    </m-card>
  </div>
</template>
<script setup>
import { ref, onMounted, onBeforeUnmount, nextTick } from "vue"
import * as echarts from "echarts"
import mCard from "@/components/mCard/index.vue"
import VChart from "vue-echarts"
import "echarts-gl"

const option = ref({
  title: {
    text: "亿元",
    left: "5%",
    top: "8%",
    textStyle: {
      color: "#D3F8F2",
      fontSize: 8,
    },
  },
  grid3D: {
    boxWidth: 200,
    boxDepth: 80,
    boxHeight: 100,
    alpha: 20,
    beta: 40,
    light: {
      main: {
        intensity: 1.2,
        shadow: true,
        shadowQuality: 'high',
        alpha: 30,
        beta: 40
      },
      ambient: {
        intensity: 0.3
      }
    },
    environment: 'none',
    postEffect: {
      enable: true,
      bloom: {
        enable: true,
        bloomIntensity: 0.1
      }
    },
    viewControl: {
      alpha: 20,
      beta: 40,
      distance: 200,
      autoRotate: false
    }
  },
  tooltip: {
    trigger: "axis",
    backgroundColor: "rgba(0,0,0,1)",
    borderWidth: 1,
    borderColor: "#999999",
    textStyle: {
      color: "#ffffff",
      fontSize: 10,
    },
  },
  xAxis3D: {
    type: "category",
    data: [
      "2023/04",
      "2023/05",
      "2023/06",
      "2023/07",
      "2023/08",
      "2023/09",
      "2023/10",
      "2023/11",
      "2023/12",
      "2024/01",
      "2024/02",
      "2024/03",
    ],
    axisLabel: {
      color: "#ffffff",
      fontSize: 8,
    },
    axisLine: {
      lineStyle: {
        color: "#435459"
      }
    }
  },
  yAxis3D: {
    type: "value",
    axisLabel: {
      color: "#8B9EA4",
      fontSize: 8,
    },
    axisLine: {
      lineStyle: {
        color: "#435459"
      }
    },
    splitLine: {
      lineStyle: {
        color: "rgba(255, 255, 255, 0.3)"
      }
    }
  },
  zAxis3D: {
    type: "value",
    min: 0,
    max: 50,
    axisLabel: {
      color: "#8B9EA4",
      fontSize: 8,
    }
  },
  series: [
    {
      type: "surface",
      wireframe: {
        show: false
      },
      equation: {
        x: {
          step: 0.1,
          min: 0,
          max: 11
        },
        y: {
          step: 20,
          min: 0,
          max: 1400
        },
        z: function (x, y) {
          const data = [500, 1000, 300, 1300, 500, 1330, 620, 400, 700, 1300, 300, 1234];
          const index = Math.floor(x);
          if (index >= 0 && index < data.length) {
            const height = data[index];
            const distance = Math.abs(y - height);
            return Math.max(0, 50 - distance * 0.05);
          }
          return 0;
        }
      },
      itemStyle: {
        color: function(params) {
          const z = params.data[2];
          const ratio = z / 50;
          if (ratio > 0.8) {
            return new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: "rgba(115, 208, 255, 1)" },
              { offset: 1, color: "rgba(25, 255, 236, 0.8)" }
            ]);
          } else if (ratio > 0.3) {
            return new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: "rgba(25, 255, 236, 0.6)" },
              { offset: 1, color: "rgba(51, 153, 255, 0.4)" }
            ]);
          } else {
            return "rgba(51, 153, 255, 0.1)";
          }
        }
      },
      shading: 'realistic',
      realisticMaterial: {
        roughness: 0.2,
        metalness: 0.1
      }
    }
  ]
})

</script>
<style lang="scss"></style>
